// Binary to decimal conversion
export function binaryToDecimal(binary: string): number {
  // Remove spaces and validate binary string
  const cleanBinary = binary.replace(/\s/g, '');
  if (!/^[01]+$/.test(cleanBinary)) {
    throw new Error('Invalid binary number');
  }
  return parseInt(cleanBinary, 2);
}

// Decimal to binary conversion
export function decimalToBinary(decimal: number, bits: number = 8): string {
  if (!Number.isInteger(decimal)) {
    throw new Error('Please enter a valid integer');
  }
  // Handle negative numbers using two's complement
  if (decimal < 0) {
    const maxValue = Math.pow(2, bits);
    decimal = maxValue + decimal;
  }
  return decimal.toString(2).padStart(bits, '0');
}

// Binary operations
export function binaryAND(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, '0');
  const num2 = b.padStart(len, '0');
  let result = '';
  
  for (let i = 0; i < len; i++) {
    result += (Number(num1[i]) & Number(num2[i])).toString();
  }
  return result;
}

export function binaryOR(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, '0');
  const num2 = b.padStart(len, '0');
  let result = '';
  
  for (let i = 0; i < len; i++) {
    result += (Number(num1[i]) | Number(num2[i])).toString();
  }
  return result;
}

export function binaryXOR(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, '0');
  const num2 = b.padStart(len, '0');
  let result = '';
  
  for (let i = 0; i < len; i++) {
    result += (Number(num1[i]) ^ Number(num2[i])).toString();
  }
  return result;
}

export function binaryNOT(a: string): string {
  return a.split('').map(bit => bit === '1' ? '0' : '1').join('');
}

// Binary addition
export function binaryAdd(a: string, b: string): string {
  const len = Math.max(a.length, b.length);
  const num1 = a.padStart(len, '0');
  const num2 = b.padStart(len, '0');
  let carry = 0;
  let result = '';
  
  for (let i = len - 1; i >= 0; i--) {
    const sum = Number(num1[i]) + Number(num2[i]) + carry;
    result = (sum % 2).toString() + result;
    carry = Math.floor(sum / 2);
  }
  
  if (carry) result = '1' + result;
  return result;
}

// Validate binary string
export function isValidBinary(binary: string): boolean {
  return /^[01]+$/.test(binary.replace(/\s/g, ''));
}

// Format binary string with spaces for readability
export function formatBinary(binary: string): string {
  const clean = binary.replace(/\s/g, '');
  return clean.match(/.{1,4}/g)?.join(' ') || clean;
}

// Quick reference data
export const binaryReference = {
  powers: Array.from({ length: 8 }, (_, i) => ({
    power: i,
    value: Math.pow(2, i),
    binary: decimalToBinary(Math.pow(2, i), 8)
  })),
  commonValues: [
    { decimal: 0, binary: '0000' },
    { decimal: 1, binary: '0001' },
    { decimal: 2, binary: '0010' },
    { decimal: 4, binary: '0100' },
    { decimal: 8, binary: '1000' },
    { decimal: 15, binary: '1111' },
  ]
};