"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useRef, useState, useEffect } from 'react';
import { cn } from "@/lib/utils";
import {
  Type,
  Replace,
  Calendar,
  Binary,
  FileCode,
  MessageSquareQuote,
  Wand2,
  Key,
  Heart,
  Search,
  QrCode,
  Video,
  ChevronLeft,
  ChevronRight,
  UtensilsCrossed,
  Database,
  FileText,
  FileDown
} from 'lucide-react';

interface ToolsNavProps {
  className?: string;
}

export function ToolsNav({ className }: ToolsNavProps) {
  const pathname = usePathname();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);

  const checkScroll = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  };

  useEffect(() => {
    checkScroll();
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  const scroll = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (container) {
      const scrollAmount = container.clientWidth * 0.8;
      container.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  // Add to the tools array (around line 85-90)
  const tools = [
    // Text Formatting
    {
      title: "Text Conversion",
      href: "/",
      icon: Type,
    },
    {
      title: "Date Calculator",
      href: "/date-calculator",
      icon: Calendar,
    },
    {
      title: "Replace Spaces",
      href: "/replace-spaces",
      icon: Replace,
    },
    {
      title: "Find and Replace",
      href: "/find-replace",
      icon: Search,
    },
    {
      title: "QR Generator",
      href: "/qr-code",
      icon: QrCode,
    },
    {
      title: "Data Processor",
      href: "/data-processor",
      icon: Database,
    },
    // PDF Tools
    {
      title: "PDF Tools",
      href: "/pdf-tools",
      icon: FileText,
    },
    // Developer Tools
    {
      title: "Binary Calculator",
      href: "/binary-calculator",
      icon: Binary,
    },
    {
      title: "Code Formatter",
      href: "/code-formatter",
      icon: FileCode,
    },
    {
      title: "Password Generator",
      href: "/password-generator",
      icon: Key,
    },
    // AI-Powered Tools
    {
      title: "Paraphrase Tool",
      href: "/paraphrase",
      icon: MessageSquareQuote,
    },
    {
      title: "Text Humanizer",
      href: "/humanizer",
      icon: Wand2,
    },
    {
      title: "Markdown Converter",
      href: "/markdown-converter",
      icon: FileDown,
    },
    {
      title: "Pickup Line Generator",
      href: "/pickup-line-generator",
      icon: Heart,
    },
    {
      title: "Recipe Generator",
      href: "/recipe-generator",
      icon: UtensilsCrossed,
    },
    {
      title: "Unicode Finder",
      href: "/unicode-finder",
      icon: FileText,
    },
  ] as const;

  return (
    <div className={cn("bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700", className)}>
      <div className="w-full max-w-[1280px] lg:max-w-[1280px] xl:max-w-[1440px] 2xl:max-w-[1536px] 3xl:max-w-[1920px] 4xl:max-w-[2560px] mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="relative flex items-center">
          {showLeftArrow && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 z-10 p-2 rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md hover:bg-white dark:hover:bg-gray-700 transition-colors"
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
          )}

          <div
            ref={scrollContainerRef}
            onScroll={checkScroll}
            className="flex items-center space-x-4 sm:space-x-6 lg:space-x-8 3xl:space-x-10 4xl:space-x-12 h-8 3xl:h-12 4xl:h-14 overflow-x-auto scrollbar-hide"
            style={{
              msOverflowStyle: 'none',
              scrollbarWidth: 'none',
            }}
          >
            {tools.map((tool) => {
              const Icon = tool.icon;
              const isActive = pathname === tool.href;
              return tool.href === '/markdown-converter' ? (
                <a
                  key={tool.href}
                  href={tool.href}
                  className={cn(
                    "flex items-center space-x-1 sm:space-x-2 hover:text-gray-900 dark:hover:text-white min-w-max transition-colors duration-200",
                    isActive && "text-gray-900 dark:text-white border-b border-gray-900 dark:border-white"
                  )}
                >
                  <Icon className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 3xl:h-6 3xl:w-6 4xl:h-7 4xl:w-7" />
                  <span className="text-xs sm:text-sm lg:text-base 3xl:text-lg 4xl:text-xl">{tool.title}</span>
                </a>
              ) : (
                <Link
                  key={tool.href}
                  href={tool.href}
                  className={cn(
                    "flex items-center space-x-1 sm:space-x-2 hover:text-gray-900 dark:hover:text-white min-w-max transition-colors duration-200",
                    isActive && "text-gray-900 dark:text-white border-b border-gray-900 dark:border-white"
                  )}
                >
                  <Icon className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 3xl:h-6 3xl:w-6 4xl:h-7 4xl:w-7" />
                  <span className="text-xs sm:text-sm lg:text-base 3xl:text-lg 4xl:text-xl">{tool.title}</span>
                </Link>
              );
            })}
          </div>

          {showRightArrow && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 z-10 p-2 rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md hover:bg-white dark:hover:bg-gray-700 transition-colors"
              aria-label="Scroll right"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
