'use client';

import { Toaster } from "sonner"
import Script from 'next/script'
import { Navbar } from '@/components/Navbar'
import { Analytics } from "@vercel/analytics/react"
import { UserPreferencesProvider } from '@/contexts/UserPreferencesContext'
import { AuthProvider } from '@/contexts/AuthContext'
import { SpeedInsights } from "@vercel/speed-insights/next"

interface ClientLayoutProps {
  children: React.ReactNode;
}

export function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <>
      <Script id="schema-script" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Text Converter",
            "description": "A collection of text conversion and manipulation tools.",
            "applicationCategory": "Text Editor",
            "url": "https://freetextconverter.com",
            "author": {
              "@type": "Person",
              "name": "Text Converter"
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            }
          }
        `}
      </Script>
      <UserPreferencesProvider>
        <AuthProvider>
          <div className="flex min-h-screen">
            {/* Left Sidebar */}
            <div className="hidden lg:block sticky top-0 h-screen">
              <Navbar className="h-full border-r border-gray-200 dark:border-gray-700" />
            </div>

            {/* Mobile Navbar */}
            <div className="lg:hidden">
              <Navbar className="border-r border-gray-200 dark:border-gray-700" />
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col min-h-screen">
              <main className="flex-1 w-full">
                {children}
              </main>
            </div>
          </div>
        </AuthProvider>
      </UserPreferencesProvider>

      <Toaster />
      <Analytics />
      <SpeedInsights />
    </>
  );
}
