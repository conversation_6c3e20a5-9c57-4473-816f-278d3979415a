'use client';

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useTheme, themes as allThemes } from "@/contexts/ThemeContext";
import { useUserPreferences } from "@/contexts/UserPreferencesContext";
import { ThemeSelector } from "@/components/ThemeSelector";
import {
  Menu,
  Sun,
  Moon,
  Type,
  Replace,
  Calendar,
  Binary,
  FileCode,
  MessageSquareQuote,
  Wand2,
  Key,
  Heart,
  Search,
  QrCode,
  Video,
  ChevronLeft,
  ChevronRight,
  UtensilsCrossed,
  X,
  Menu as MenuIcon,
  BookOpen,
  Mail,
  LayoutGrid,
  Palette,
  Image as ImageIcon,
  Database,
  Clock,
  Ruler,
  FileText,
  FilePlus,
  FileDown,
  FileUp,
  FileImage,
  RotateCw,
  Scissors,
  Star,
  ChevronDown,
  ChevronUp,
  FileOutput,
  FileInput,
  FileSpreadsheet,
  FileLock,
  FileKey,
  FileSearch,
  FileSliders,
  FileWarning,
  FileX
} from "lucide-react";

interface NavbarProps {
  className?: string;
}

type ThemeType = 'native' | 'green' | 'dark' | 'rose' | 'amber' | 'cyan' | 'sunset' | 'ocean' | 'midnight' | 'aurora' | 'candy' | 'forest' | 'lavender' | 'crimson' | 'sapphire' | 'emerald' | 'volcano' | 'galaxy' | 'neon' | 'vintage' | 'monochrome' | 'tropical' | 'arctic' | 'desert' | 'cosmic' | 'royal';

interface ThemeStyles {
  bg: string;
  hover: string;
  text: string;
  border: string;
  category: string;
}

type Themes = Record<ThemeType, ThemeStyles>;

export function Navbar({ className }: NavbarProps) {
  const { currentTheme, setCurrentTheme, isDarkMode, setIsDarkMode } = useTheme();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Use themes from ThemeContext instead of hardcoded ones
  const themes = allThemes;

  // Get theme styles, defaulting to native if currentTheme is invalid
  const themeStyles = themes[currentTheme] || themes.native;

  // Ensure we always have valid theme styles
  if (!themeStyles || !themeStyles.bg) {
    console.warn('Invalid theme styles detected, falling back to native');
    const fallbackStyles = themes.native;
    return (
      <nav className={cn(
        fallbackStyles.bg,
        fallbackStyles.text,
        "fixed top-0 bottom-0 left-0 w-[250px] z-40 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static",
        !isMobileMenuOpen && "-translate-x-full",
        isCollapsed ? "lg:w-20" : "lg:w-64",
        "lg:border-r lg:border-white/5 shadow-[1px_0_2px_rgba(255,255,255,0.05)]",
        className
      )}>
        <div className="text-white p-4">Theme Error - Please refresh</div>
      </nav>
    );
  }

  useEffect(() => {
    document.documentElement.classList.toggle("dark", isDarkMode);
  }, [isDarkMode]);

  const { preferences, toggleFavorite, toggleCategoryCollapse, isFavorite, isCategoryCollapsed } = useUserPreferences();

  const tools = [
    {
      category: "Text Tools",
      items: [
        {
          title: "Text Conversion",
          href: "/",
          icon: Type,
          iconColor: "text-blue-400",
          description: "Convert text to different styles and formats"
        },
        {
          title: "Replace Spaces",
          href: "/replace-spaces",
          icon: Replace,
          iconColor: "text-green-400",
          description: "Replace spaces with other characters"
        },
        {
          title: "Find and Replace",
          href: "/find-replace",
          icon: Search,
          iconColor: "text-purple-400",
          description: "Powerful find and replace functionality"
        },
        {
          title: "Unicode Finder",
          href: "/unicode-finder",
          icon: FileSearch,
          iconColor: "text-indigo-400",
          description: "Find and replace hidden Unicode characters"
        },
        {
          title: "Email Signature",
          href: "/email-signature",
          icon: Mail,
          iconColor: "text-pink-400"
        },
        {
          title: "Markdown Converter",
          href: "/markdown-converter",
          icon: FileText,
          iconColor: "text-cyan-400",
          description: "Convert between Markdown, HTML, and plain text"
        },
      ]
    },
    {
      category: "Utilities",
      items: [
        {
          title: "QR Generator",
          href: "/qr-code",
          icon: QrCode,
          iconColor: "text-emerald-400"
        },
        {
          title: "Date Calculator",
          href: "/date-calculator",
          icon: Calendar,
          iconColor: "text-violet-400"
        },
        {
          title: "Unit Converter",
          href: "/unit-converter",
          icon: Ruler,
          iconColor: "text-orange-400"
        },
        {
          title: "Image Tools",
          href: "/image-tools",
          icon: ImageIcon,
          iconColor: "text-pink-400"
        }
      ]
    },
    {
      category: "PDF Tools",
      items: [
        {
          title: "PDF Tools Home",
          href: "/pdf-tools",
          icon: FileText,
          iconColor: "text-red-400"
        },
        // Basic PDF Operations
        {
          title: "Merge PDF",
          href: "/pdf-tools/merge",
          icon: FilePlus,
          iconColor: "text-blue-400"
        },
        {
          title: "Split PDF",
          href: "/pdf-tools/split",
          icon: Scissors,
          iconColor: "text-purple-400"
        },
        {
          title: "Compress PDF",
          href: "/pdf-tools/compress",
          icon: FileDown,
          iconColor: "text-green-400"
        },
        {
          title: "Rotate PDF",
          href: "/pdf-tools/rotate",
          icon: RotateCw,
          iconColor: "text-orange-400"
        },
        // PDF Conversion
        {
          title: "PDF to Word",
          href: "/pdf-tools/pdf-to-word",
          icon: FileOutput,
          iconColor: "text-blue-400"
        },
        {
          title: "Word to PDF",
          href: "/pdf-tools/word-to-pdf",
          icon: FileInput,
          iconColor: "text-indigo-400"
        },
        {
          title: "PDF to JPG",
          href: "/pdf-tools/pdf-to-jpg",
          icon: FileImage,
          iconColor: "text-pink-400"
        },
        {
          title: "JPG to PDF",
          href: "/pdf-tools/jpg-to-pdf",
          icon: FileUp,
          iconColor: "text-red-400"
        },
        {
          title: "PDF to Excel",
          href: "/pdf-tools/pdf-to-excel",
          icon: FileSpreadsheet,
          iconColor: "text-green-400"
        },
        // Advanced PDF Tools
        {
          title: "Protect PDF",
          href: "/pdf-tools/protect",
          icon: FileLock,
          iconColor: "text-red-400"
        },
        {
          title: "Unlock PDF",
          href: "/pdf-tools/unlock",
          icon: FileKey,
          iconColor: "text-yellow-400"
        },
        {
          title: "OCR PDF",
          href: "/pdf-tools/ocr",
          icon: FileSearch,
          iconColor: "text-purple-400"
        },
        {
          title: "Organize PDF",
          href: "/pdf-tools/organize",
          icon: FileSliders,
          iconColor: "text-blue-400"
        },
        {
          title: "Repair PDF",
          href: "/pdf-tools/repair",
          icon: FileWarning,
          iconColor: "text-amber-400"
        },
        {
          title: "Watermark PDF",
          href: "/pdf-tools/watermark",
          icon: FileX,
          iconColor: "text-teal-400"
        }
      ]
    },
    {
      category: "AI Tools",
      items: [
        {
          title: "Paraphrase Tool",
          href: "/paraphrase",
          icon: MessageSquareQuote,
          iconColor: "text-pink-400"
        },
        {
          title: "Text Humanizer",
          href: "/humanizer",
          icon: Wand2,
          iconColor: "text-indigo-400"
        },
        {
          title: "Pickup Line Generator",
          href: "/pickup-line-generator",
          icon: Heart,
          iconColor: "text-rose-400"
        },
        {
          title: "Recipe Generator",
          href: "/recipe-generator",
          icon: UtensilsCrossed,
          iconColor: "text-cyan-400"
        },
        {
          title: "Image Generator",
          href: "/image-generator",
          icon: ImageIcon,
          iconColor: "text-orange-400"
        },
      ]
    },
    {
      category: "Developer Tools",
      items: [
        {
          title: "Data Processor",
          href: "/data-processor",
          icon: Database,
          iconColor: "text-emerald-400"
        },
        {
          title: "Binary Calculator",
          href: "/binary-calculator",
          icon: Binary,
          iconColor: "text-yellow-400"
        },
        {
          title: "Code Formatter",
          href: "/code-formatter",
          icon: FileCode,
          iconColor: "text-orange-400"
        },
        {
          title: "Password Generator",
          href: "/password-generator",
          icon: Key,
          iconColor: "text-red-400"
        },
        {
          title: "UUID Generator",
          href: "/uuid-generator",
          icon: Key,
          iconColor: "text-purple-400"
        },
        {
          title: "Color Converter",
          href: "/color-converter",
          icon: Palette,
          iconColor: "text-pink-400"
        },
        {
          title: "Unix Timestamp Converter",
          href: "/unix-timestamp-converter",
          icon: Clock,
          iconColor: "text-blue-400"
        },
      ]
    },
  ];

  // Get all favorited tools across categories
  const getFavoritedTools = () => {
    const favorited = [];
    for (const category of tools) {
      for (const tool of category.items) {
        if (isFavorite(tool.href)) {
          favorited.push({
            ...tool,
            category: category.category // Add category information to the tool
          });
        }
      }
    }
    return favorited;
  };

  // Filter tools based on search query
  const filterTools = (toolsArray: typeof tools) => {
    if (!searchQuery.trim()) return toolsArray;

    const query = searchQuery.toLowerCase().trim();

    return toolsArray.map(category => ({
      ...category,
      items: category.items.filter(tool =>
        tool.title.toLowerCase().includes(query) ||
        tool.description?.toLowerCase().includes(query) ||
        category.category.toLowerCase().includes(query)
      )
    })).filter(category => category.items.length > 0);
  };

  // Get filtered tools
  const filteredTools = filterTools(tools);

  // Get filtered favorites
  const getFilteredFavorites = () => {
    const favorites = getFavoritedTools();
    if (!searchQuery.trim()) return favorites;

    const query = searchQuery.toLowerCase().trim();
    return favorites.filter(tool =>
      tool.title.toLowerCase().includes(query) ||
      tool.description?.toLowerCase().includes(query) ||
      tool.category.toLowerCase().includes(query)
    );
  };

  const navigationLinks = [
    {
      title: "Guide",
      href: "/guide",
      icon: BookOpen,
    },
    {
      title: "Contact",
      href: "/contact",
      icon: Mail,
    },
  ];

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Mobile Menu Button */}
      <button
        className="lg:hidden fixed top-4 right-4 p-2 z-50 bg-[#125C88] rounded-full shadow-lg"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        {isMobileMenuOpen ? <X className="h-6 w-6 text-white" /> : <MenuIcon className="h-6 w-6 text-white" />}
      </button>

      <nav className={cn(
        themeStyles.bg, // Use the safe themeStyles
        themeStyles.text, // Use the safe themeStyles
        "fixed top-0 bottom-0 left-0 w-[250px] z-40 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static",
        !isMobileMenuOpen && "-translate-x-full",
        isCollapsed ? "lg:w-20" : "lg:w-64",
        "lg:border-r lg:border-white/5 shadow-[1px_0_2px_rgba(255,255,255,0.05)]",
        className
      )}>
        {/* Desktop Collapse Button */}
        <button
          className="hidden lg:block absolute -right-3 top-1/2 transform -translate-y-1/2 bg-[#125C88] p-1 rounded-full border border-gray-700/30 z-50 shadow-lg"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {isCollapsed ?
            <ChevronRight className="h-4 w-4 text-white" /> :
            <ChevronLeft className="h-4 w-4 text-white" />
          }
        </button>

        {/* Navbar Content */}
        <div className="flex flex-col h-full">
          {/* Logo Section - Fixed Top */}
          <div className="shrink-0 p-4">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/Pixel_and_Code Logo.svg"
                alt="Pixel&Code Logo"
                width={56}
                height={56}
                className="rounded-lg"
              />
              {!isCollapsed && (
                <span className="text-xl font-bold">Pixel&Code</span>
              )}
            </Link>
          </div>

          {/* Search Section */}
          {!isCollapsed ? (
            <div className="shrink-0 px-4 pb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search tools..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:bg-white/15 focus:border-white/30 transition-colors"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                    title="Clear search"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="shrink-0 px-4 pb-4 flex justify-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsCollapsed(false)}
                className="rounded-full hover:bg-white/10"
                title="Expand to search tools"
              >
                <Search className="h-4 w-4 text-gray-400" />
              </Button>
            </div>
          )}

          {/* Scrollable Content Section */}
          <div className="flex-1 overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
            {/* Tools Navigation */}
            <div className="py-4">
              {/* Favorites Section */}
              {preferences.favorites.length > 0 && getFilteredFavorites().length > 0 && (
                <div className="px-4 mb-6">
                  {!isCollapsed && (
                    <h2 className={cn("text-sm font-semibold mb-2 flex items-center", "text-yellow-400")}>
                      <Star className="h-3.5 w-3.5 mr-1.5 text-yellow-400" />
                      Favorites
                      {searchQuery && (
                        <span className="ml-2 text-xs text-gray-400">
                          ({getFilteredFavorites().length})
                        </span>
                      )}
                    </h2>
                  )}
                  <div className="space-y-1">
                    {getFilteredFavorites().map((tool) => {
                      const Icon = tool.icon;
                      return (
                        <div key={tool.href} className="flex items-center group">
                          {tool.href === '/markdown-converter' ? (
                            <a
                              href={tool.href}
                              onClick={() => {
                                if (window.innerWidth < 1024) {
                                  setIsMobileMenuOpen(false);
                                }
                              }}
                              className={cn(
                                "flex items-center flex-1 space-x-2 px-2 py-1.5 rounded-lg hover:bg-white/10 transition-colors",
                                isCollapsed && "justify-center"
                              )}
                              title={isCollapsed ? tool.title : undefined}
                            >
                              <Icon className={cn("h-4 w-4", tool.iconColor)} />
                              {!isCollapsed && (
                                <span className="text-sm flex-1">{tool.title}</span>
                              )}
                            </a>
                          ) : (
                            <Link
                              href={tool.href}
                              onClick={() => {
                                if (window.innerWidth < 1024) {
                                  setIsMobileMenuOpen(false);
                                }
                              }}
                              className={cn(
                                "flex items-center flex-1 space-x-2 px-2 py-1.5 rounded-lg hover:bg-white/10 transition-colors",
                                isCollapsed && "justify-center"
                              )}
                              title={isCollapsed ? tool.title : undefined}
                            >
                              <Icon className={cn("h-4 w-4", tool.iconColor)} />
                              {!isCollapsed && (
                                <span className="text-sm flex-1">{tool.title}</span>
                              )}
                            </Link>
                          )}
                          {!isCollapsed && (
                            <button
                              onClick={() => toggleFavorite(tool.href)}
                              className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:text-yellow-400"
                              title="Remove from favorites"
                            >
                              <Star className="h-3.5 w-3.5 fill-yellow-400 text-yellow-400" />
                            </button>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Regular Categories */}
              {filteredTools.map((category, index) => (
                <div key={category.category} className={cn("px-4", index > 0 && "mt-6")}>
                  {!isCollapsed && (
                    <div className="flex items-center justify-between mb-2">
                      <h2 className={cn("text-sm font-semibold", themeStyles.category)}>
                        {category.category}
                        {searchQuery && (
                          <span className="ml-2 text-xs text-gray-400">
                            ({category.items.length})
                          </span>
                        )}
                      </h2>
                      {!searchQuery && (
                        <button
                          onClick={() => toggleCategoryCollapse(category.category)}
                          className="p-1 rounded-md hover:bg-white/10 transition-colors"
                          title={isCategoryCollapsed(category.category) ? "Expand" : "Collapse"}
                        >
                          {isCategoryCollapsed(category.category) ? (
                            <ChevronDown className="h-3.5 w-3.5" />
                          ) : (
                            <ChevronUp className="h-3.5 w-3.5" />
                          )}
                        </button>
                      )}
                    </div>
                  )}

                  {(!isCategoryCollapsed(category.category) || searchQuery) && (
                    <div className="space-y-1">
                      {category.items.map((tool) => {
                        const Icon = tool.icon;
                        return (
                          <div key={tool.href} className="flex items-center group">
                            {tool.href === '/markdown-converter' ? (
                              <a
                                href={tool.href}
                                onClick={() => {
                                  if (window.innerWidth < 1024) {
                                    setIsMobileMenuOpen(false);
                                  }
                                }}
                                className={cn(
                                  "flex items-center flex-1 space-x-2 px-2 py-1.5 rounded-lg hover:bg-white/10 transition-colors",
                                  isCollapsed && "justify-center"
                                )}
                                title={isCollapsed ? tool.title : undefined}
                              >
                                <Icon className={cn("h-4 w-4", tool.iconColor)} />
                                {!isCollapsed && <span className="text-sm flex-1">{tool.title}</span>}
                              </a>
                            ) : (
                              <Link
                                href={tool.href}
                                onClick={() => {
                                  if (window.innerWidth < 1024) {
                                    setIsMobileMenuOpen(false);
                                  }
                                }}
                                className={cn(
                                  "flex items-center flex-1 space-x-2 px-2 py-1.5 rounded-lg hover:bg-white/10 transition-colors",
                                  isCollapsed && "justify-center"
                                )}
                                title={isCollapsed ? tool.title : undefined}
                              >
                                <Icon className={cn("h-4 w-4", tool.iconColor)} />
                                {!isCollapsed && <span className="text-sm flex-1">{tool.title}</span>}
                              </Link>
                            )}
                            {!isCollapsed && (
                              <button
                                onClick={() => toggleFavorite(tool.href)}
                                className={cn(
                                  "opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:text-yellow-400",
                                  isFavorite(tool.href) && "opacity-100"
                                )}
                                title={isFavorite(tool.href) ? "Remove from favorites" : "Add to favorites"}
                              >
                                <Star
                                  className={cn(
                                    "h-3.5 w-3.5",
                                    isFavorite(tool.href) ? "fill-yellow-400 text-yellow-400" : "text-gray-400"
                                  )}
                                />
                              </button>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}

              {/* No Results Message */}
              {searchQuery && filteredTools.length === 0 && getFilteredFavorites().length === 0 && (
                <div className="px-4 py-8 text-center">
                  <Search className="h-8 w-8 mx-auto mb-3 text-gray-400 opacity-50" />
                  <p className="text-sm text-gray-400 mb-1">No tools found</p>
                  <p className="text-xs text-gray-500">
                    Try searching with different keywords
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchQuery("")}
                    className="mt-3 text-xs text-gray-400 hover:text-white"
                  >
                    Clear search
                  </Button>
                </div>
              )}
            </div>

            {/* Navigation Links */}
            <div className={cn("px-4 mt-6")}>
              {!isCollapsed && (
                <h2 className="text-sm font-semibold text-gray-300 mb-2">Navigation</h2>
              )}
              <div className="space-y-1">
                <Link
                  href="/all-tools"
                  onClick={() => {
                    if (window.innerWidth < 1024) {
                      setIsMobileMenuOpen(false);
                    }
                  }}
                  className={cn(
                    "flex items-center space-x-2 px-2 py-1.5 rounded-lg hover:bg-white/10 transition-colors",
                    isCollapsed && "justify-center"
                  )}
                  title={isCollapsed ? "All Tools" : undefined}
                >
                  <LayoutGrid className={cn("h-4 w-4", "text-white")} />
                  {!isCollapsed && <span className="text-sm">All Tools</span>}
                </Link>
                {navigationLinks.map((link) => {
                  const Icon = link.icon;
                  return (
                    <Link
                      key={link.href}
                      href={link.href}
                      onClick={() => {
                        if (window.innerWidth < 1024) {
                          setIsMobileMenuOpen(false);
                        }
                      }}
                      className={cn(
                        "flex items-center space-x-2 px-2 py-1.5 rounded-lg hover:bg-white/10 transition-colors",
                        isCollapsed && "justify-center"
                      )}
                      title={isCollapsed ? link.title : undefined}
                    >
                      <Icon className="h-4 w-4" />
                      {!isCollapsed && <span className="text-sm">{link.title}</span>}
                    </Link>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Theme Toggles at Bottom */}
          <div className="shrink-0 p-4">
            <div className="flex items-center justify-center gap-4">
              {/* Dark/Light Mode Toggle */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsDarkMode(!isDarkMode)}
                className={cn(themeStyles.hover, "rounded-full")}
                title={isDarkMode ? "Light Mode" : "Dark Mode"}
              >
                {isDarkMode ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </Button>

              {/* Theme Selector */}
              <ThemeSelector />
            </div>
          </div>
        </div>
      </nav>
    </>
  );
}
