'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { binaryToDecimal, decimalToBinary, isValidBinary, formatBinary } from '@/lib/binaryUtils';

export function BinaryCalculator() {
  const [binaryInput, setBinaryInput] = useState('');
  const [decimalInput, setDecimalInput] = useState('');
  const [bits, setBits] = useState('8');
  const [error, setError] = useState('');

  const handleBinaryChange = (value: string) => {
    setBinaryInput(value);
    if (!value) {
      setDecimalInput('');
      setError('');
      return;
    }

    try {
      if (isValidBinary(value)) {
        const decimal = binaryToDecimal(value);
        setDecimalInput(decimal.toString());
        setError('');
      } else {
        setError('Please enter a valid binary number (0s and 1s only)');
      }
    } catch (err) {
      setError((err as Error).message);
    }
  };

  const handleDecimalChange = (value: string) => {
    setDecimalInput(value);
    if (!value) {
      setBinaryInput('');
      setError('');
      return;
    }

    try {
      const decimal = parseInt(value);
      if (isNaN(decimal)) {
        setError('Please enter a valid decimal number');
        return;
      }
      const maxValue = Math.pow(2, parseInt(bits)) - 1;
      const minValue = -Math.pow(2, parseInt(bits) - 1);
      
      if (decimal > maxValue || decimal < minValue) {
        setError(`Number must be between ${minValue} and ${maxValue} for ${bits}-bit binary`);
        return;
      }
      
      const binary = decimalToBinary(decimal, parseInt(bits));
      setBinaryInput(formatBinary(binary));
      setError('');
    } catch (err) {
      setError((err as Error).message);
    }
  };

  const handleClear = () => {
    setBinaryInput('');
    setDecimalInput('');
    setError('');
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Binary ⇄ Decimal Converter</h2>
          <div className="flex items-center space-x-2">
            <Label htmlFor="bits">Bits:</Label>
            <Select value={bits} onValueChange={setBits}>
              <SelectTrigger className="w-20">
                <SelectValue placeholder="Bits" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="4">4</SelectItem>
                <SelectItem value="8">8</SelectItem>
                <SelectItem value="16">16</SelectItem>
                <SelectItem value="32">32</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="binary">Binary Number</Label>
            <Input
              id="binary"
              placeholder="Enter binary number (e.g., 1010)"
              value={binaryInput}
              onChange={(e) => handleBinaryChange(e.target.value)}
              className="font-mono bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="decimal">Decimal Number</Label>
            <Input
              id="decimal"
              placeholder="Enter decimal number"
              value={decimalInput}
              onChange={(e) => handleDecimalChange(e.target.value)}
              className="font-mono bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            />
          </div>
        </div>

        {error && (
          <p className="text-red-500 text-sm">{error}</p>
        )}

        <Button onClick={handleClear} variant="outline" className="w-full">
          Clear
        </Button>
      </div>

      <div className="bg-slate-50 dark:bg-gray-900 rounded-lg p-4">
        <h3 className="font-medium mb-2">Quick Tips:</h3>
        <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-300">
          <li>• Binary numbers only use 0s and 1s</li>
          <li>• Each position represents a power of 2</li>
          <li>• {bits}-bit binary can represent numbers from {-Math.pow(2, parseInt(bits) - 1)} to {Math.pow(2, parseInt(bits)) - 1}</li>
          <li>• Leading zeros don't change the value</li>
        </ul>
      </div>
    </div>
  );
}