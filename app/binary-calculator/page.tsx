import { BinaryCalculator } from "@/components/BinaryCalculator";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Binary Calculator - Free Online Binary Number Converter',
  description: 'Free online binary calculator for converting numbers between binary, decimal, hexadecimal, and octal formats. Easy to use number system converter.',
  keywords: 'binary calculator, binary converter, decimal to binary, hex converter, octal converter',
  openGraph: {
    title: 'Binary Calculator - Free Online Binary Number Converter',
    description: 'Free online binary calculator for converting numbers between binary, decimal, hexadecimal, and octal formats.',
    url: 'https://freetextconverter.com/binary-calculator',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Binary Calculator - Free Online Binary Number Converter',
    description: 'Free online binary calculator for converting numbers between binary, decimal, hexadecimal, and octal formats.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/binary-calculator'
  }
};

export default function BinaryCalculatorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Binary Calculator
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Perform binary calculations and conversions
              </p>

              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Binary Operations</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Add, subtract, multiply, and divide</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Base Conversion</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Convert between binary and decimal</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Quick Copy</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Copy results with one click</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative">
            {/* Decorative background elements */}
            <div className="absolute -top-20 -right-20 w-64 h-64 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-3xl -z-10"></div>
            <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-gradient-to-tr from-pink-400/5 to-yellow-400/5 rounded-full blur-3xl -z-10"></div>

            {/* Card with pattern background */}
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Decorative pattern */}
              <div className="absolute inset-0 opacity-5 dark:opacity-10 pointer-events-none">
                <div className="absolute inset-0 bg-grid-gray-900/[0.2] [mask-image:linear-gradient(to_bottom,white,transparent)]"
                     style={{ backgroundSize: '32px 32px' }}></div>
              </div>

              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"></div>

              {/* Content */}
              <div className="p-6">
                <BinaryCalculator />
              </div>
            </div>
          </div>

          {/* Guide Section */}
          <div className="relative mt-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-green-500 via-teal-500 to-cyan-500"></div>

              <div className="p-6 space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Number System Conversion Guide
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Learn how to use our binary calculator and understand number systems
                  </p>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-blue-700 dark:text-blue-400">Supported Number Systems</h3>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Binary (Base-2)</li>
                      <li>Decimal (Base-10)</li>
                      <li>Hexadecimal (Base-16)</li>
                      <li>Octal (Base-8)</li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-purple-700 dark:text-purple-400">Features</h3>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Real-time conversion</li>
                      <li>Error validation</li>
                      <li>Copy to clipboard</li>
                      <li>Responsive design</li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-pink-700 dark:text-pink-400">Common Uses</h3>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Programming & Development</li>
                      <li>Computer Science Education</li>
                      <li>Digital Electronics</li>
                      <li>Data Analysis</li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                        <svg className="h-5 w-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <h3 className="font-semibold text-yellow-700 dark:text-yellow-400">How to Use</h3>
                    </div>
                    <ol className="list-decimal list-inside space-y-1 text-sm">
                      <li>Enter a number in any format</li>
                      <li>See instant conversions</li>
                      <li>Click to copy results</li>
                      <li>Switch between number systems</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
