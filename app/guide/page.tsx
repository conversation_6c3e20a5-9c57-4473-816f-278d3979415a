import { CombinedGuide } from "@/components/CombinedGuide";
import { Footer } from "@/components/Footer";
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Comprehensive Guide - Text Conversion and Space Replacement',
  description: 'Explore all features and tools available in our text conversion and space replacement guides.',
};

export default function GuidePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-grow">
        <CombinedGuide />
      </main>
      <Footer />
    </div>
  );
}