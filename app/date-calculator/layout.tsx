import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Date Calculator - Free Online Date Difference & Working Days Calculator',
  description: 'Free online date calculator. Calculate date differences, add/subtract days, count working days, and find weekdays. Perfect for project planning and scheduling.',
  keywords: 'date calculator, working days calculator, date difference, add days calculator, weekday calculator, business days',
  openGraph: {
    title: 'Date Calculator - Free Online Date Difference Calculator',
    description: 'Calculate date differences, working days, and more with our free online date calculator. Perfect for project planning and scheduling.',
    type: 'website',
    locale: 'en_US',
    siteName: 'Text Converter',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Online Date Calculator',
    description: 'Calculate date differences, working days, and more. Perfect for project planning.',
  },
  alternates: {
    canonical: 'https://textconverter.io/date-calculator',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
};

export default function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div itemScope itemType="https://schema.org/WebApplication">
      <meta itemProp="name" content="Date Calculator" />
      <meta itemProp="description" content="Free online date calculator for calculating date differences, working days, and more." />
      <meta itemProp="applicationCategory" content="UtilityApplication" />
      <meta itemProp="operatingSystem" content="Any" />
      {children}
    </div>
  );
}