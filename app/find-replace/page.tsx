import { Card, CardContent } from "@/components/ui/card";
import { FindAndReplace } from "@/components/FindAndReplace";
import FindAndReplaceGuide from "@/components/FindAndReplaceGuide";
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Metadata } from 'next';




export const metadata: Metadata = {
  title: 'Find and Replace - Online Text Search and Replace Tool',
  description: 'Free online tool to find and replace text with support for regular expressions. Perfect for text editing and document processing.',
  keywords: 'find and replace, text search, replace text, regex search, text editor',
  openGraph: {
    title: 'Find and Replace - Online Text Search and Replace Tool',
    description: 'Free online tool to find and replace text with support for regular expressions.',
    url: 'https://freetextconverter.com/find-replace',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Find and Replace - Online Text Search and Replace Tool',
    description: 'Free online tool to find and replace text with support for regular expressions.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/find-replace'
  }
};

export default function FindAndReplacePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section with unique gradient */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Find and Replace
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Find and replace text with powerful pattern matching
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Pattern Matching</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Support for regex and wildcards</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Bulk Replace</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Replace all occurrences at once</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Preview Changes</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">See changes before applying</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Tool Section */}
          <FindAndReplace />

          {/* Guide Section */}
          <FindAndReplaceGuide />

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      
      {/* Footer */}
      <Footer />
    </div>
  );
}