'use client';

import { Type, Replace, Calendar, Binary, FileCode, MessageSquareQuote, Wand2, Key, Search, QrCode, Video, Image as ImageIcon, FileText, FilePlus, FileImage, FileDown } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Footer } from "@/components/Footer";

const toolCategories = [
  {
    name: "Text Formatting",
    description: "Tools for transforming and formatting your text",
    tools: [
      {
        name: "Text Conversion",
        description: "Transform your text faster than your coffee gets cold! Perfect for when your CAPS LOCK gets stuck or when you're feeling a bit ~fancy~.",
        icon: Type,
        href: "/",
        color: "text-blue-500",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
      },
      {
        name: "Replace Spaces",
        description: "Spaces giving you trouble? We'll fix that faster than you can say 'why_do_programmers_hate_spaces?' Perfect for those URL nightmares!",
        icon: Replace,
        href: "/replace-spaces",
        color: "text-green-500",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
      },
      {
        name: "Find and Replace",
        description: "Search and replace text with powerful regex support. Make bulk changes to your text with precision.",
        icon: Search,
        href: "/find-replace",
        color: "text-purple-500",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200",
      },
      {
        name: "QR Generator",
        description: "Create, customize, and track QR codes for your URLs and text. Perfect for sharing links and information in a visual format.",
        icon: QrCode,
        href: "/qr-code",
        color: "text-pink-500",
        bgColor: "bg-pink-50",
        borderColor: "border-pink-200",
      },
      {
        name: "Video Downloader",
        description: "Download and convert videos from various platforms. Convert videos to different formats including MP3 and MP4.",
        icon: Video,
        href: "/video-downloader",
        color: "text-red-500",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
      },
      {
        name: "Image Tools",
        description: "Transform and optimize your images with ease. Crop, resize, rotate, and apply various transformations to your images.",
        icon: ImageIcon,
        href: "/image-tools",
        color: "text-pink-500",
        bgColor: "bg-pink-50",
        borderColor: "border-pink-200",
      }
    ]
  },
  {
    name: "Developer Tools",
    description: "Essential tools for developers",
    tools: [
      {
        name: "Binary Calculator",
        description: "For when you need to speak robot! Convert numbers faster than you can say '1010101'.",
        icon: Binary,
        href: "/binary-calculator",
        color: "text-orange-500",
        bgColor: "bg-orange-50",
        borderColor: "border-orange-200",
      },
      {
        name: "Code Formatter",
        description: "Turn your spaghetti code into a masterpiece! Because even your messy code deserves to look pretty.",
        icon: FileCode,
        href: "/code-formatter",
        color: "text-red-500",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
      },
      {
        name: "Password Generator",
        description: "Create unbreakable passwords faster than you can say 'password123'! Generate secure passwords that even your tech-savvy cat can't guess.",
        icon: Key,
        href: "/password-generator",
        color: "text-indigo-500",
        bgColor: "bg-indigo-50",
        borderColor: "border-indigo-200",
      }
    ]
  },
  {
    name: "AI-Powered Tools",
    description: "Smart tools powered by artificial intelligence",
    tools: [
      {
        name: "Paraphrase Tool",
        description: "Like having a thesaurus-powered ninja! Rewrite text so smooth, even your English teacher would be proud.",
        icon: MessageSquareQuote,
        href: "/paraphrase",
        color: "text-purple-500",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200",
      },
      {
        name: "Text Humanizer",
        description: "Turn robot-speak into human-friendly chat! Makes AI text sound less... artificial!",
        icon: Wand2,
        href: "/humanizer",
        color: "text-pink-500",
        bgColor: "bg-pink-50",
        borderColor: "border-pink-200",
      },
      {
        name: "Image Generator",
        description: "Transform your ideas into stunning images with AI! Create unique visuals for any project or concept.",
        icon: ImageIcon,
        href: "/image-generator",
        color: "text-blue-500",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
      }
    ]
  },
  {
    name: "PDF Tools",
    description: "Work with PDF files easily",
    tools: [
      {
        name: "PDF Tools",
        description: "Every tool you need to work with PDFs in one place. Merge, split, compress, convert, and more!",
        icon: FileText,
        href: "/pdf-tools",
        color: "text-red-500",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
      },
      {
        name: "Merge PDF",
        description: "Combine multiple PDF files into a single document with our free online PDF merger tool.",
        icon: FilePlus,
        href: "/pdf-tools/merge",
        color: "text-blue-500",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
      },
      {
        name: "PDF to JPG",
        description: "Convert PDF pages to JPG images. Extract all images from your PDF documents.",
        icon: FileImage,
        href: "/pdf-tools/pdf-to-jpg",
        color: "text-purple-500",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200",
      }
    ]
  },
  {
    name: "Utility Tools",
    description: "Handy tools for everyday use",
    tools: [
      {
        name: "Date Calculator",
        description: "Calculate dates like a time-traveling wizard! Find out how many days between dates.",
        icon: Calendar,
        href: "/date-calculator",
        color: "text-purple-500",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200",
      }
    ]
  }
];

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { y: 20, opacity: 0 },
  show: { y: 0, opacity: 1 }
};

const AllToolsPage = () => {
  return (
    <>
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-indigo-400/90 via-purple-400/90 to-pink-400/90 text-white py-12 3xl:py-16 4xl:py-20 mb-8">
          <div className="container mx-auto px-4 3xl:max-w-7xl 4xl:max-w-[1800px]">
            <h1 className="text-4xl md:text-5xl 3xl:text-6xl 4xl:text-7xl font-bold text-center mb-4 text-white">
              Text Tools that Actually Work! 🚀
            </h1>
            <p className="text-xl 3xl:text-2xl 4xl:text-3xl text-center text-white/90 max-w-2xl 3xl:max-w-3xl 4xl:max-w-4xl mx-auto">
              Because life's too short for boring text tools! We've got everything you need, minus the headaches and coffee spills. ☕️
            </p>
          </div>
        </div>

        {/* Tools Grid */}
        <div className="container mx-auto py-12 3xl:py-16 4xl:py-20 px-4">
          <h1 className="text-4xl 3xl:text-5xl 4xl:text-6xl font-bold text-center mb-8 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            All Tools
          </h1>
          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
            className="space-y-12"
          >
            {toolCategories.map((category, index) => (
              <div key={index} className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-semibold mb-2">{category.name}</h2>
                  <p className="text-gray-600 dark:text-gray-400">{category.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {category.tools.map((tool, toolIndex) => (
                    <motion.div key={toolIndex} variants={item}>
                      <Link href={tool.href}>
                        <div className={`p-6 rounded-lg border ${tool.borderColor} hover:border-gray-300 transition-all duration-300 h-full`}>
                          <div className={`w-12 h-12 rounded-lg ${tool.bgColor} flex items-center justify-center mb-4`}>
                            <tool.icon className={`w-6 h-6 ${tool.color}`} />
                          </div>
                          <h3 className="text-xl font-semibold mb-2">{tool.name}</h3>
                          <p className="text-gray-600 dark:text-gray-400">{tool.description}</p>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AllToolsPage;
