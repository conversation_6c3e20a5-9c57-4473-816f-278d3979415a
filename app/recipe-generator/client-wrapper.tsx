'use client';

import dynamic from 'next/dynamic';

// Use dynamic import with ssr:false in a client component
const RecipeGeneratorClientPage = dynamic(
  () => import('./client-page'),
  { ssr: false, loading: () => <LoadingComponent /> }
);

function LoadingComponent() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-rose-50 via-orange-50 to-amber-50">
      <div className="w-16 h-16 mb-4 relative">
        <div className="absolute inset-0 rounded-full border-4 border-amber-200 opacity-25"></div>
        <div className="absolute inset-0 rounded-full border-4 border-amber-600 opacity-75 animate-spin border-t-transparent"></div>
      </div>
      <h3 className="text-xl font-semibold text-amber-600 mb-2">Loading Recipe Generator...</h3>
      <p className="text-gray-600 max-w-md text-center">
        Preparing our recipe tools for you...
      </p>
    </div>
  );
}

export default function ClientPage() {
  return <RecipeGeneratorClientPage />;
} 