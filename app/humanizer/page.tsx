import { HumanizerTool } from "@/components/HumanizerTool";
import { Footer } from "@/components/Footer";
import { BuyMeCoffeeButton } from "@/components/BuyMeCoffeeButton";
import { Metadata } from 'next';
import { Bo<PERSON>, GraduationCap, Sparkles } from 'lucide-react';
import { Card } from "@/components/ui/card";
import { SupportSection } from "@/components/SupportSection";
import { useEffect, useState } from 'react';

export const metadata: Metadata = {
  title: 'AI Text Humanizer - Transform Your Writing Style',
  description: 'Transform your text into natural, human-like writing with our AI-powered Humanizer. Choose from different education levels to match your desired writing style.',
  keywords: 'text humanizer, AI writing assistant, natural text generator, education levels writing, human-like text',
  openGraph: {
    title: 'AI Text Humanizer - Transform Your Writing Style',
    description: 'Transform your text into natural, human-like writing with our AI-powered Humanizer.',
    url: 'https://freetextconverter.com/humanizer',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Text Humanizer - Transform Your Writing Style',
    description: 'Transform your text into natural, human-like writing with our AI-powered Humanizer.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/humanizer'
  }
};

export default function HumanizerPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-gray-800 rounded-lg mb-6">
            <div className="max-w-4xl mx-auto px-4 pt-6 pb-12 sm:pt-8 sm:pb-16 lg:pt-10 lg:pb-20 text-center">
              <h1 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Text Humanizer
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-6">
                Make your text sound more natural and human-like
              </p>
              
              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Natural Language</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Convert to conversational tone</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Style Options</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Choose from multiple tones</p>
                </div>
                <div className="flex flex-col items-center p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-pink-100 dark:bg-pink-900 flex items-center justify-center mb-2">
                    <svg className="w-5 h-5 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-semibold mb-1">Instant Results</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 text-center">Get humanized text instantly</p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Content */}
          <div className="max-w-5xl mx-auto px-4">
            <div className="grid gap-8">
              {/* Tool Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-purple-100 dark:border-purple-900">
                <HumanizerTool />
              </div>

              {/* Guide Section */}
              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card className="p-6 bg-gradient-to-br from-purple-50 to-white dark:from-gray-800 dark:to-gray-700">
                    <h3 className="text-lg font-semibold mb-3 text-purple-600 dark:text-purple-400">Features</h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• AI-powered text transformation</li>
                      <li>• Three writing style levels</li>
                      <li>• Natural language processing</li>
                      <li>• Up to 100 words per request</li>
                      <li>• Rate limit: 1 request per 15 seconds</li>
                    </ul>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-700">
                    <h3 className="text-lg font-semibold mb-3 text-blue-600 dark:text-blue-400">Writing Levels</h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• <span className="font-medium">Basic:</span> Simple and casual writing style</li>
                      <li>• <span className="font-medium">Medium:</span> Balanced complexity with natural flow</li>
                      <li>• <span className="font-medium">Advanced:</span> Sophisticated language with professional tone</li>
                    </ul>
                  </Card>
                </div>

                <Card className="p-6 bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-700">
                  <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">How to Use</h3>
                  <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                    <li className="flex gap-3">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-semibold">1</span>
                      <span>Enter your text in the input box (maximum 100 words)</span>
                    </li>
                    <li className="flex gap-3">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-semibold">2</span>
                      <span>Choose your desired writing level (Basic, Medium, or Advanced)</span>
                    </li>
                    <li className="flex gap-3">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-semibold">3</span>
                      <span>Click "Humanize" to transform your text</span>
                    </li>
                    <li className="flex gap-3">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-semibold">4</span>
                      <span>Copy the humanized text using the "Copy" button</span>
                    </li>
                  </ol>
                </Card>
              </div>

              {/* Support Section */}
              <SupportSection />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}