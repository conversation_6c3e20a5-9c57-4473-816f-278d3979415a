import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { PDFProtectToolClient } from '@/components/pdf-tools/client/PDFProtectToolClient';

export const metadata: Metadata = {
  title: 'Protect PDF - Text Converter',
  description: 'Secure your PDF documents with password protection and permissions',
  keywords: ['protect pdf', 'password protect pdf', 'secure pdf', 'pdf security', 'pdf permissions'],
  openGraph: {
    title: 'Protect PDF - Add Password to PDF Files Online',
    description: 'Add password protection to your PDF files with our free online PDF encryption tool.',
    url: 'https://freetextconverter.com/pdf-tools/protect',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Protect PDF - Add Password to PDF Files Online',
    description: 'Add password protection to your PDF files with our free online PDF encryption tool.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/protect'
  }
};

export default function ProtectPDFPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-red-50 via-rose-50 to-pink-50 dark:from-gray-900 dark:via-red-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-red-600 to-rose-600 bg-clip-text text-transparent">
                Protect PDF with Password
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Add password protection to your PDF files to keep them secure. Simple, free, and works right in your browser.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-red-600 via-rose-600 to-red-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFProtectToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Password Protect a PDF</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your PDF file into the upload area.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Set a Password</h3>
                    <p className="text-gray-600 dark:text-gray-400">Enter the password that will be required to open the PDF. Optionally, set a separate owner password.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Configure Permissions</h3>
                    <p className="text-gray-600 dark:text-gray-400">Choose which actions are allowed for users who open your PDF with the password (printing, copying, etc.)</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your Protected PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the encryption process is complete, download your password-protected PDF file.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my PDF data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What&apos;s the difference between user and owner passwords?</h3>
                <p className="text-gray-600 dark:text-gray-400">A user password is required to open the document. An owner password allows full access to the document, including changing permissions. If only a user password is set, it&apos;s also used as the owner password.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What&apos;s the maximum file size?</h3>
                <p className="text-gray-600 dark:text-gray-400">Each PDF file can be up to 100MB in size. This limit ensures optimal performance while still accommodating most PDF documents.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What if I forget my password?</h3>
                <p className="text-gray-600 dark:text-gray-400">Since encryption happens in your browser and we don&apos;t store your password or file, we cannot recover your password. Make sure to keep it in a safe place.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
} 