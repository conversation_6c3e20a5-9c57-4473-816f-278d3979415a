import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { PDFUnlockToolClient } from '@/components/pdf-tools/client/PDFUnlockToolClient';
import { Unlock } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Unlock PDF - Text Converter',
  description: 'Remove password protection from your PDF documents with our free online PDF Unlock tool',
  keywords: ['unlock pdf', 'pdf password remover', 'remove pdf password', 'pdf unlocker', 'decrypt pdf', 'online pdf tools'],
  openGraph: {
    title: 'Unlock PDF - Remove Password from PDF Files Online',
    description: 'Remove password protection from your PDF documents with our free online PDF Unlock tool.',
    url: 'https://freetextconverter.com/pdf-tools/unlock',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Unlock PDF - Remove Password from PDF Files Online',
    description: 'Remove password protection from your PDF documents with our free online PDF Unlock tool.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/unlock'
  }
};

export default function UnlockPDFPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-blue-50 via-indigo-50 to-sky-50 dark:from-gray-900 dark:via-blue-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Unlock PDF Documents
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Remove password protection from your PDF files. Simple, free, and works right in your browser.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFUnlockToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Unlock a PDF</h2>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your password-protected PDF file into the upload area.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Enter Password</h3>
                    <p className="text-gray-600 dark:text-gray-400">Type in the correct password for your PDF document.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Unlocked PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the unlocking process is complete, download your PDF file without password protection.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my PDF data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files and passwords are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Do I need to know the password?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, our tool requires the correct password to unlock the PDF. This is not a password recovery tool but rather a tool to remove password protection from files you own and have the password for.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What&apos;s the maximum file size?</h3>
                <p className="text-gray-600 dark:text-gray-400">Each PDF file can be up to 100MB in size. This limit ensures optimal performance while still accommodating most PDF documents.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What if I forgot my PDF password?</h3>
                <p className="text-gray-600 dark:text-gray-400">Unfortunately, if you don&apos;t know the password, this tool cannot help you recover it. You might need to contact the document creator or use specialized password recovery software.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
} 