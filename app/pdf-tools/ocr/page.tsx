import type { Metadata } from 'next';
import { FileSearch } from 'lucide-react';
import PDFOCRToolClient from '@/components/pdf-tools/client/PDFOCRToolClient';
import Script from 'next/script';

export const metadata: Metadata = {
  title: 'OCR PDF - Make Scanned PDFs Searchable & Editable | Text Converter',
  description: 'Convert scanned image-based PDFs to searchable, editable text documents with our free OCR (Optical Character Recognition) tool. Extract text while preserving original layout and formatting. Process locally in your browser for complete privacy.',
  keywords: ['OCR PDF', 'PDF text recognition', 'searchable PDF', 'extract text from scanned PDF', 'PDF optical character recognition', 'scanned PDF to text', 'make PDF searchable', 'image to text PDF', 'PDF OCR online', 'free PDF OCR', 'recognize text in PDF'],
  openGraph: {
    title: 'OCR PDF - Make Scanned PDFs Searchable & Editable',
    description: 'Convert image-based PDFs to searchable documents with our free online OCR tool. Extract text while preserving original layout.',
    url: 'https://freetextconverter.com/pdf-tools/ocr',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: 'https://freetextconverter.com/og-images/pdf-ocr.png',
        width: 1200,
        height: 630,
        alt: 'PDF OCR Tool',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OCR PDF - Make Scanned PDFs Searchable & Editable',
    description: 'Convert image-based PDFs to searchable documents with our free online OCR tool. Extract text while preserving original layout.',
    images: ['https://freetextconverter.com/og-images/pdf-ocr.png'],
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/ocr'
  }
};

export default function PDFOCRPage() {
  return (
    <>
      <Script id="schema-pdf-ocr" type="application/ld+json" dangerouslySetInnerHTML={{ 
        __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "OCR PDF - Make Scanned PDFs Searchable",
          "applicationCategory": "WebApplication",
          "operatingSystem": "Any",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "description": "Convert scanned image-based PDFs to searchable text documents. Extract text while preserving original layout and formatting.",
          "featureList": [
            "Browser-based OCR processing",
            "Multiple language support",
            "Quality settings adjustment",
            "Private and secure processing"
          ],
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.7",
            "ratingCount": "142"
          }
        })
      }} />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-12 mb-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900 dark:text-white">
                OCR PDF - Make Scanned PDFs Searchable
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                Convert scanned PDFs to searchable documents with optical character recognition (OCR). Extract text from image-based PDFs while maintaining the original layout.
              </p>
            </div>
            <div className="md:w-1/3 flex justify-center">
              <div className="w-48 h-48 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                <FileSearch className="h-24 w-24 text-purple-500 dark:text-purple-400" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Tool Section */}
      <section className="container mx-auto px-4 py-8 mb-12">
        <div className="max-w-4xl mx-auto">
          <PDFOCRToolClient />
        </div>
      </section>

      {/* How to Use Section */}
      <section className="container mx-auto px-4 py-8 mb-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">
            How to Use OCR on Your PDF Files
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-purple-600 dark:text-purple-400 font-bold text-lg">1</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Upload Your PDF
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select a PDF file from your device or drag and drop it directly into the upload area.
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-purple-600 dark:text-purple-400 font-bold text-lg">2</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Choose OCR Settings
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select the OCR language and quality settings. Higher quality settings will take more time but provide better text recognition.
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-purple-600 dark:text-purple-400 font-bold text-lg">3</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Download Your Searchable PDF
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Process the file and download your searchable PDF. All processing happens in your browser for maximum privacy.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What is OCR and how does it work?</h3>
            <p className="text-gray-600 dark:text-gray-400">OCR (Optical Character Recognition) is a technology that converts images of text into machine-readable text. When applied to PDFs, it analyzes the images in the document, identifies characters and words, and creates a text layer on top of the original document.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What types of documents work best with OCR?</h3>
            <p className="text-gray-600 dark:text-gray-400">OCR works best with clearly scanned documents with good contrast between text and background. Typed text yields better results than handwriting. Documents with simple layouts are easier to process than complex ones with multiple columns, tables, or mixed text and images.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure when using this tool?</h3>
            <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What languages are supported for OCR?</h3>
            <p className="text-gray-600 dark:text-gray-400">Our OCR tool supports multiple languages including English, Spanish, French, German, Italian, Portuguese, Dutch, and many more. Select the appropriate language for your document to improve recognition accuracy.</p>
          </div>
        </div>
      </div>

      {/* Support Section */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">Need Help with PDF OCR?</h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
              If you&apos;re experiencing any issues with our OCR tool or have suggestions for improvements, we&apos;re here to help.
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <a href="/contact" className="inline-block px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors duration-200">
              Contact Support
            </a>
          </div>
        </div>
      </div>
    </>
  );
} 