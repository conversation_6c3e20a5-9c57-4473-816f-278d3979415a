import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { RotateCw } from 'lucide-react';
import { PDFRotateToolClient } from "@/components/pdf-tools/client/PDFRotateToolClient";

export const metadata: Metadata = {
  title: 'Rotate PDF - Change PDF Page Orientation Online',
  description: 'Rotate PDF pages to the correct orientation with our free online PDF rotation tool. No installation or registration required.',
  keywords: 'rotate pdf, change pdf orientation, pdf rotation, rotate pdf pages',
  openGraph: {
    title: 'Rotate PDF - Change PDF Page Orientation Online',
    description: 'Rotate PDF pages to the correct orientation with our free online PDF rotation tool.',
    url: 'https://freetextconverter.com/pdf-tools/rotate',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Rotate PDF - Change PDF Page Orientation Online',
    description: 'Rotate PDF pages to the correct orientation with our free online PDF rotation tool.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/rotate'
  }
};

export default function RotatePDFPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 dark:from-gray-900 dark:via-orange-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                Rotate PDF Pages
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Change the orientation of your PDF pages with ease. Free, secure, and no installation required.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-orange-600 via-amber-600 to-yellow-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFRotateToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Rotate PDF Pages</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your PDF file into the upload area.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Select Rotation Angle and Mode</h3>
                    <p className="text-gray-600 dark:text-gray-400">Choose the rotation angle (90°, 180°, or 270°) and select either single or continuous rotation mode.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Rotate PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the "Rotate PDF" button to apply the rotation. In continuous mode, you can keep rotating multiple times.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your Rotated PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once you're satisfied with the rotation, download your PDF file with the new orientation.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">About Continuous Rotation Mode</h3>
              <p className="text-gray-600 dark:text-gray-400">
                The continuous rotation mode allows you to keep rotating your PDF in the same direction multiple times.
                This is useful when you need to make precise adjustments to the orientation or when you want to rotate
                a document multiple times without downloading between each rotation. You can track the total rotation
                angle and reset to the original orientation at any time.
              </p>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Can I rotate only specific pages in my PDF?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, our tool allows you to select specific pages to rotate. You can rotate individual pages, page ranges, or all pages in your document.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What rotation angles are available?</h3>
                <p className="text-gray-600 dark:text-gray-400">You can rotate pages by 90° clockwise, 90° counterclockwise, or 180° (upside down). This covers all possible orientation needs.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Can I continuously rotate my PDF?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, our tool offers a continuous rotation mode that allows you to keep rotating your PDF multiple times in the same direction. This is useful when you need to make precise adjustments to the orientation.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Will rotating affect the quality of my PDF?</h3>
                <p className="text-gray-600 dark:text-gray-400">No, rotating PDF pages is a lossless operation. The quality of your document will remain exactly the same after rotation.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
