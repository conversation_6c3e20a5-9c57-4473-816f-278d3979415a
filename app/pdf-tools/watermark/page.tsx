import { Metadata } from 'next';
import PDFWatermarkToolClient from '@/components/pdf-tools/client/PDFWatermarkToolClient';
import { SupportSection } from '@/components/SupportSection';
import Script from 'next/script';

export const metadata: Metadata = {
  title: 'Add Watermark to PDF - Free Online PDF Watermarking Tool | Text Converter',
  description: 'Add text or image watermarks to your PDF files online. Customize opacity, rotation, position, and apply to specific pages. Free, secure, browser-based tool with no installation required.',
  keywords: 'watermark PDF, add watermark to PDF, PDF watermarking tool, text watermark, image watermark, online PDF tool, PDF branding, confidential PDF, copyright PDF, PDF protection, add logo to PDF, PDF stamp',
  openGraph: {
    title: 'Add Watermark to PDF - Free Online PDF Watermarking Tool',
    description: 'Add text or image watermarks to your PDF files. Customize opacity, rotation, position with our free browser-based tool.',
    url: 'https://freetextconverter.com/pdf-tools/watermark',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: 'https://freetextconverter.com/og-images/pdf-watermark.png',
        width: 1200,
        height: 630,
        alt: 'PDF Watermark Tool',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Add Watermark to PDF - Free Online PDF Watermarking Tool',
    description: 'Add text or image watermarks to your PDF files with our free browser-based tool.',
    images: ['https://freetextconverter.com/og-images/pdf-watermark.png'],
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/watermark'
  }
};

export default function WatermarkPDFPage() {
  // How to use steps
  const howToUseSteps = [
    {
      title: 'Upload your PDF file',
      description: 'Select and upload the PDF file you want to add a watermark to.'
    },
    {
      title: 'Choose watermark type',
      description: 'Select between text or image watermark and configure the settings.'
    },
    {
      title: 'Add watermark and download',
      description: 'Click "Add Watermark" and download your watermarked PDF file.'
    }
  ];

  // FAQ items
  const faqItems = [
    {
      question: 'Is it safe to watermark my PDF online?',
      answer: 'Yes, our tool processes files locally in your browser. Your files and data are never uploaded to our servers, ensuring complete privacy and security.'
    },
    {
      question: 'What types of watermarks can I add?',
      answer: 'You can add text watermarks with customizable text, font size, color, and opacity. You can also add image watermarks from your device with adjustable opacity and size.'
    },
    {
      question: 'Can I position the watermark anywhere on the document?',
      answer: 'Yes, you can position your watermark in various locations including center, corners, or custom positions on the page.'
    },
    {
      question: 'Will the watermark appear on all pages?',
      answer: 'Yes, the watermark will be applied to all pages of your PDF document by default.'
    },
    {
      question: 'What\'s the maximum file size I can watermark?',
      answer: 'You can watermark PDF files up to 50MB. For larger files, we recommend splitting the PDF into smaller parts first.'
    }
  ];

  return (
    <div className="container mx-auto px-4">
      <Script id="schema-pdf-watermark" type="application/ld+json" dangerouslySetInnerHTML={{ 
        __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "PDF Watermark Tool",
          "applicationCategory": "WebApplication",
          "operatingSystem": "Any",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "description": "Add custom text or image watermarks to your PDF documents with adjustable opacity, rotation, and positioning.",
          "featureList": [
            "Text and image watermarks",
            "Custom positioning",
            "Opacity and rotation control",
            "Page selection options",
            "Local browser-based processing"
          ],
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "138"
          }
        })
      }} />
      
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-gray-900 dark:to-gray-800 rounded-lg p-8 mb-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">
            Add Watermark to PDF
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Add custom text or image watermarks to your PDF documents
          </p>
        </div>
      </div>

      {/* Main Tool Section */}
      <div className="bg-card rounded-lg shadow-lg p-6 my-8">
        <PDFWatermarkToolClient />
      </div>

      {/* How to Use Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700 my-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">
          How to Add Watermark to PDF
        </h2>
        <div className="grid md:grid-cols-3 gap-6">
          {howToUseSteps.map((step, index) => (
            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-amber-600 dark:text-amber-400 font-bold">{index + 1}</span>
              </div>
              <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">{step.title}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700 my-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">
          Frequently Asked Questions
        </h2>
        <div className="space-y-4">
          {faqItems.map((item, index) => (
            <div key={index} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">{item.question}</h3>
              <p className="text-gray-600 dark:text-gray-400">{item.answer}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Support Section */}
      <SupportSection />
    </div>
  );
} 