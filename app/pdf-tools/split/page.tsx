import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { FileText, Scissors } from 'lucide-react';
import { PDFSplitToolClient } from "@/components/pdf-tools/client/PDFSplitToolClient";

export const metadata: Metadata = {
  title: 'Split PDF - Extract Pages from PDF Files Online',
  description: 'Extract pages from PDF files and save them as separate documents with our free online PDF splitter tool. No installation or registration required.',
  keywords: 'split pdf, extract pdf pages, pdf splitter, pdf page extractor, separate pdf',
  openGraph: {
    title: 'Split PDF - Extract Pages from PDF Files Online',
    description: 'Extract pages from PDF files and save them as separate documents with our free online PDF splitter tool.',
    url: 'https://freetextconverter.com/pdf-tools/split',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Split PDF - Extract Pages from PDF Files Online',
    description: 'Extract pages from PDF files and save them as separate documents with our free online PDF splitter tool.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/split'
  }
};

export default function SplitPDFPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Split PDF Files
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Extract pages from your PDF documents and save them as separate files. Free, secure, and no installation required.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFSplitToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Split PDF Files</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your PDF file into the upload area.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Select Pages to Extract</h3>
                    <p className="text-gray-600 dark:text-gray-400">Choose which pages you want to extract from your PDF. You can select individual pages, ranges, or all pages.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Split PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the "Split PDF" button to extract the selected pages from your PDF document.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your Split PDFs</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the splitting process is complete, download your extracted PDF pages as separate files or as a ZIP archive.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What is a PDF splitter?</h3>
                <p className="text-gray-600 dark:text-gray-400">A PDF splitter is a tool that allows you to extract specific pages from a PDF document and save them as separate PDF files. It's useful when you only need certain pages from a larger document.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What's the maximum file size?</h3>
                <p className="text-gray-600 dark:text-gray-400">Each PDF file can be up to 100MB in size. This limit ensures optimal performance while still accommodating most PDF documents.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Will the split PDFs maintain the original quality?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, our tool preserves the original quality of your PDF files. There is no loss in resolution or formatting during the splitting process.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
