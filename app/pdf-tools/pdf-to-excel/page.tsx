import { Metadata } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { PDFToExcelToolClient } from "@/components/pdf-tools/client/PDFToExcelToolClient";

export const metadata: Metadata = {
  title: 'PDF to Excel - Extract Tables from PDF to Excel Online',
  description: 'Convert tables and data from PDF to Excel spreadsheets with our free online converter. No installation or registration required.',
  keywords: 'pdf to excel, pdf to spreadsheet, convert pdf to xlsx, extract tables from pdf, pdf data extraction',
  openGraph: {
    title: 'PDF to Excel - Extract Tables from PDF to Excel Online',
    description: 'Convert tables and data from PDF to Excel spreadsheets with our free online converter.',
    url: 'https://freetextconverter.com/pdf-tools/pdf-to-excel',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PDF to Excel - Extract Tables from PDF to Excel Online',
    description: 'Convert tables and data from PDF to Excel spreadsheets with our free online converter.',
  },
  alternates: {
    canonical: 'https://freetextconverter.com/pdf-tools/pdf-to-excel'
  }
};

export default function PDFToExcelPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-green-50 via-emerald-50 to-blue-50 dark:from-gray-900 dark:via-green-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-6 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                PDF to Excel Converter
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Extract tables and data from PDF documents to Excel spreadsheets. Free, secure, and no installation required.
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-12">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600"></div>

              {/* Content */}
              <div className="p-6">
                <PDFToExcelToolClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Convert PDF to Excel</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Upload Your PDF</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the upload button or drag and drop your PDF file into the upload area. PDFs with tables, forms, or structured data work best.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Select Pages (Optional)</h3>
                    <p className="text-gray-600 dark:text-gray-400">You can choose to extract data from all pages or select specific pages containing the tables you need.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Convert to Excel</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the "Convert to Excel" button to extract the data from your PDF and convert it to Excel format.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Download Your Excel File</h3>
                    <p className="text-gray-600 dark:text-gray-400">Once the conversion is complete, download your new Excel spreadsheet and start working with your data.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">How accurate is the PDF to Excel conversion?</h3>
                <p className="text-gray-600 dark:text-gray-400">Our tool uses advanced table extraction algorithms to accurately convert PDF tables to Excel format. The accuracy depends on the structure and quality of the original PDF. Well-structured tables with clear borders typically yield the best results.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What types of PDFs work best with this converter?</h3>
                <p className="text-gray-600 dark:text-gray-400">PDFs with native digital tables work best. Scanned documents with tables may require OCR processing first, which can reduce accuracy. For optimal results, use PDFs that were created digitally rather than scanned from paper.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your files are never uploaded to our servers, ensuring complete privacy and security for your sensitive data.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What's the maximum file size I can convert?</h3>
                <p className="text-gray-600 dark:text-gray-400">You can convert PDF files up to 50MB in size. This limit ensures optimal performance while still accommodating most business documents with data tables.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
} 