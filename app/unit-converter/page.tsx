import type { Metadata } from "next";
import { Card } from "@/components/ui/card";
import { SupportSection } from "@/components/SupportSection";
import UnitConverterWrapper from "@/components/UnitConverterWrapper";
import { Ruler, ArrowRightLeft, Zap } from "lucide-react";
import { Footer } from "@/components/Footer";

export const metadata: Metadata = {
  title: "Unit Converter | TextConverter",
  description:
    "Convert between various units of measurement including length, weight, volume, temperature, area, and speed. Free online unit conversion tool.",
};

export default function UnitConverterPage() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <div className="w-full">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 dark:from-gray-900 dark:via-emerald-900/20 dark:to-gray-900">
          <div className="max-w-5xl mx-auto px-4 pt-8 pb-12 sm:pt-10 sm:pb-16">
            <div className="text-center space-y-8">
              <h1 className="text-4xl sm:text-5xl font-bold tracking-tight bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 dark:from-emerald-400 dark:via-teal-400 dark:to-cyan-400 bg-clip-text text-transparent">
                Unit Converter
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Convert between various units of measurement including length, weight,
                volume, temperature, area, and speed. Simple, accurate, and free to use.
              </p>

              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-12 h-12 rounded-full bg-emerald-100 dark:bg-emerald-900/50 flex items-center justify-center mb-3">
                    <Ruler className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Multiple Units</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Convert between various measurement units with ease
                  </p>
                </div>
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-12 h-12 rounded-full bg-teal-100 dark:bg-teal-900/50 flex items-center justify-center mb-3">
                    <ArrowRightLeft className="w-6 h-6 text-teal-600 dark:text-teal-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Instant Conversion</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Real-time conversion with high precision
                  </p>
                </div>
                <div className="flex flex-col items-center p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:transform hover:scale-105 transition-transform">
                  <div className="w-12 h-12 rounded-full bg-cyan-100 dark:bg-cyan-900/50 flex items-center justify-center mb-3">
                    <Zap className="w-6 h-6 text-cyan-600 dark:text-cyan-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Easy to Use</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Simple interface with copy functionality
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <div className="max-w-5xl mx-auto px-4 py-12">
          {/* Main Converter */}
          <section className="mb-16">
            <UnitConverterWrapper />
          </section>

          {/* Features Section */}
          <section className="mb-16">
            <h2 className="text-3xl font-semibold mb-8 text-center bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 dark:from-emerald-400 dark:via-teal-400 dark:to-cyan-400 bg-clip-text text-transparent">
              Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="p-6 bg-gradient-to-br from-white/80 via-white/50 to-white/30 dark:from-gray-900/80 dark:via-gray-900/50 dark:to-gray-900/30 backdrop-blur-xl border border-gray-200/50 dark:border-gray-800/50 group hover:border-emerald-500/50 transition-colors">
                <h3 className="font-semibold mb-2 text-gray-900 dark:text-gray-100 group-hover:text-emerald-500 transition-colors">Multiple Unit Types</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Convert between length, weight, volume, temperature, area, and speed
                  measurements with ease.
                </p>
              </Card>
              <Card className="p-6 bg-gradient-to-br from-white/80 via-white/50 to-white/30 dark:from-gray-900/80 dark:via-gray-900/50 dark:to-gray-900/30 backdrop-blur-xl border border-gray-200/50 dark:border-gray-800/50 group hover:border-teal-500/50 transition-colors">
                <h3 className="font-semibold mb-2 text-gray-900 dark:text-gray-100 group-hover:text-teal-500 transition-colors">Precise Calculations</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Get accurate conversions with up to 6 decimal places of precision
                  when needed.
                </p>
              </Card>
              <Card className="p-6 bg-gradient-to-br from-white/80 via-white/50 to-white/30 dark:from-gray-900/80 dark:via-gray-900/50 dark:to-gray-900/30 backdrop-blur-xl border border-gray-200/50 dark:border-gray-800/50 group hover:border-cyan-500/50 transition-colors">
                <h3 className="font-semibold mb-2 text-gray-900 dark:text-gray-100 group-hover:text-cyan-500 transition-colors">Easy to Use</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Simple interface with instant conversions and copy-to-clipboard
                  functionality.
                </p>
              </Card>
            </div>
          </section>

          {/* FAQ Section */}
          <section className="max-w-4xl mx-auto py-8">
            <h2 className="text-3xl font-semibold mb-8 text-center bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 dark:from-emerald-400 dark:via-teal-400 dark:to-cyan-400 bg-clip-text text-transparent">
              Frequently Asked Questions
            </h2>
            <div className="space-y-6 border border-gray-200/50 dark:border-gray-800/50 rounded-xl p-6 bg-gradient-to-br from-white/50 via-white/30 to-white/10 dark:from-gray-900/50 dark:via-gray-900/30 dark:to-gray-900/10 backdrop-blur-xl">
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors">
                <h3 className="text-base font-medium mb-2 text-gray-900 dark:text-gray-100">
                  How accurate are the conversions?
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Our unit converter uses precise conversion factors and provides
                  results with up to 6 decimal places when needed. All calculations
                  are based on standard international conversion rates.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors">
                <h3 className="text-base font-medium mb-2 text-gray-900 dark:text-gray-100">
                  What types of units can I convert?
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  You can convert between various units of length, weight, volume,
                  temperature, area, and speed. Each category includes both metric and
                  imperial units for comprehensive coverage.
                </p>
              </div>
              <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors">
                <h3 className="text-base font-medium mb-2 text-gray-900 dark:text-gray-100">
                  Can I copy the converted values?
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Yes! Each converted value has a copy button that lets you quickly
                  copy the result to your clipboard. This makes it easy to use the
                  converted values in other applications.
                </p>
              </div>
            </div>
          </section>

          {/* Support Section */}
          <div className="mt-16">
            <SupportSection />
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
}
