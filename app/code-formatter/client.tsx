'use client';

import { Suspense, useState } from 'react';
import { CodeEditorPanel } from '@/components/CodeEditorPanel';

export function ClientCodeFormatter() {
  const [inputCode, setInputCode] = useState('');
  const [outputCode, setOutputCode] = useState('');
  const [language, setLanguage] = useState('javascript');

  // TODO: Implement actual code formatting logic
  const handleInputChange = (value: string) => {
    setInputCode(value);
    // For now, just copy the input to output
    setOutputCode(value);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Suspense fallback={<div>Loading...</div>}>
          <CodeEditorPanel
            title="Input Code"
            code={inputCode}
            language={language}
            onCodeChange={handleInputChange}
            onLanguageChange={setLanguage}
          />
        </Suspense>
        <Suspense fallback={<div>Loading...</div>}>
          <CodeEditorPanel
            title="Formatted Code"
            code={outputCode}
            language={language}
            readOnly
          />
        </Suspense>
      </div>
    </div>
  );
}
