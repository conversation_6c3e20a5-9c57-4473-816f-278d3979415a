'use client';

import React from 'react';
import ImageGenerator from '@/components/ImageGenerator';
import { Footer } from "@/components/Footer";
import { AuthProvider } from '@/contexts/AuthContext';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Wand2, PartyPopper, Heart, Star, Zap, Image as ImageIcon, Camera, Brush, Shapes } from 'lucide-react';

const imageQuotes = [
  "Where imagination meets Ultra AI - Create stunning visuals! ",
  "Your creativity, amplified by Flux 1.1 Ultra intelligence ",
  "Transform words into ultra-quality masterpieces ",
  "Unlimited creativity with advanced editing at your fingertips ",
  "From imagination to ultra-reality in seconds ",
  "Create and edit art beyond boundaries ",
  "Your vision, our Ultra AI magic ",
  "Unleash your creative potential with Kontext editing ",
  "Making professional-grade AI art accessible to everyone ",
  "Every prompt and edit tells a unique story ",
];

const FloatingIcon = ({ icon: Icon, className }: { icon: any, className: string }) => (
  <div className={`absolute animate-float ${className}`}>
    <Icon className="w-8 h-8" />
  </div>
);

export default function Page() {
  const randomQuote = imageQuotes[Math.floor(Math.random() * imageQuotes.length)];

  return (
    <AuthProvider>
      <div className="min-h-screen bg-background overflow-hidden">
      <div className="relative">
        {/* Hero Background with Gradient */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-indigo-200 via-purple-200 to-pink-200 dark:from-indigo-950 dark:via-purple-950 dark:to-pink-950 h-[400px] opacity-70" />

        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-grid-white/10 bg-[size:30px_30px] h-[400px] [mask-image:radial-gradient(ellipse_at_center,white,transparent_75%)]" />

        {/* Floating Icons */}
        <div className="absolute inset-0 overflow-hidden">
          <FloatingIcon icon={ImageIcon} className="left-[10%] top-[20%] text-indigo-500/30 dark:text-indigo-300/30" />
          <FloatingIcon icon={Palette} className="right-[25%] top-[15%] text-purple-500/30 dark:text-purple-300/30" />
          <FloatingIcon icon={Camera} className="right-[15%] top-[35%] text-pink-500/30 dark:text-pink-300/30" />
          <FloatingIcon icon={Brush} className="left-[20%] top-[40%] text-blue-500/30 dark:text-blue-300/30" />
          <FloatingIcon icon={Shapes} className="right-[35%] top-[25%] text-violet-500/30 dark:text-violet-300/30" />
          <FloatingIcon icon={Star} className="left-[35%] top-[30%] text-amber-500/30 dark:text-amber-300/30" />
          <FloatingIcon icon={Zap} className="right-[10%] top-[20%] text-blue-500/30 dark:text-blue-300/30" />
        </div>

        {/* Content */}
        <div className="w-[90%] max-w-[2000px] mx-auto px-4">
          <div className="pt-16 pb-24 text-center">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/80 dark:bg-white/10 backdrop-blur-sm border border-indigo-100 dark:border-indigo-800 mb-6 animate-bounce-slow">
              <Sparkles className="w-4 h-4 text-indigo-500 mr-2" />
              <span className="text-sm font-medium bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                Powered by Advanced AI
              </span>
            </div>

            <h1 className="text-5xl sm:text-6xl font-bold mb-6 animate-fade-in">
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 dark:from-indigo-400 dark:via-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                AI Image Generator
              </span>
            </h1>

            <p className="text-xl text-muted-foreground mb-4 max-w-3xl mx-auto animate-fade-in-up">
              Transform your ideas into ultra-quality visuals with Flux 1.1 Ultra technology.
              Create, edit, and enhance images with professional-grade AI capabilities!
            </p>

            <p className="text-sm text-muted-foreground italic max-w-xl mx-auto mb-8 animate-fade-in-up delay-200">
              {randomQuote}
            </p>

            <div className="flex flex-wrap gap-4 justify-center mb-8 animate-fade-in-up delay-300">
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 dark:bg-white/10 backdrop-blur-sm border border-indigo-100/20 dark:border-indigo-800/20">
                <Wand2 className="w-4 h-4 text-indigo-500" />
                <span className="text-sm">AI-Powered</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 dark:bg-white/10 backdrop-blur-sm border border-purple-100/20 dark:border-purple-800/20">
                <Palette className="w-4 h-4 text-purple-500" />
                <span className="text-sm">Ultra Quality</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 dark:bg-white/10 backdrop-blur-sm border border-pink-100/20 dark:border-pink-800/20">
                <Camera className="w-4 h-4 text-pink-500" />
                <span className="text-sm">Advanced Editing</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 dark:bg-white/10 backdrop-blur-sm border border-blue-100/20 dark:border-blue-800/20">
                <Zap className="w-4 h-4 text-blue-500" />
                <span className="text-sm">Flux 1.1 Pro</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="w-[90%] max-w-[2000px] mx-auto px-4 -mt-20">
        <ImageGenerator />
      </div>

      {/* Footer - Full width to touch sidebar */}
      <div className="mt-16">
        <Footer />
      </div>

      <style jsx global>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(5deg); }
        }
        @keyframes bounce-slow {
          0%, 100% { transform: translateY(-2%); }
          50% { transform: translateY(2%); }
        }
        @keyframes fade-in {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes fade-in-up {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        .animate-bounce-slow {
          animation: bounce-slow 3s ease-in-out infinite;
        }
        .animate-fade-in {
          animation: fade-in 1s ease-out forwards;
        }
        .animate-fade-in-up {
          animation: fade-in-up 1s ease-out forwards;
        }
        .delay-200 {
          animation-delay: 200ms;
        }
        .delay-300 {
          animation-delay: 300ms;
        }
        .delay-400 {
          animation-delay: 400ms;
        }
      `}</style>
      </div>
    </AuthProvider>
  );
}
