import { <PERSON>ada<PERSON> } from 'next';
import { Footer } from "@/components/Footer";
import { SupportSection } from "@/components/SupportSection";
import { Search, Replace } from 'lucide-react';
import { UnicodeFinderClient } from "@/components/unicode/UnicodeFinderClient";
import Script from 'next/script';

export const metadata: Metadata = {
  title: 'Unicode Character Finder & Replacer - Remove Hidden Characters Left by ChatGPT & AI Tools',
  description: 'Free online Unicode character finder and replacer tool. Detect and remove invisible characters and unwanted Unicode left by AI tools like ChatGPT. Fix pasted text with hidden formatting issues and clean problematic characters.',
  keywords: 'unicode finder, hidden characters, AI Unicode markers, ChatGPT characters, non-printable characters, zero-width space, unicode replacer, text cleaner, invisible text, control characters, zero-width joiner, zero-width non-joiner, text formatting issues, clean text',
  openGraph: {
    title: 'Unicode Character Finder & Replacer - Remove Hidden Characters Left by AI Tools',
    description: 'Detect and remove zero-width spaces, invisible characters, and non-printable Unicode left by AI tools like ChatGPT that cause formatting issues in your text.',
    url: 'https://freetextconverter.com/unicode-finder',
    siteName: 'Free Text Converter',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: 'https://freetextconverter.com/og-images/unicode-finder.png',
        width: 1200,
        height: 630,
        alt: 'Unicode Character Finder tool',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Unicode Character Finder & Replacer - Remove Hidden Characters from AI Generated Text',
    description: 'Find and remove invisible Unicode characters left by ChatGPT and other AI tools in your text.',
    images: ['https://freetextconverter.com/og-images/unicode-finder.png'],
  },
  alternates: {
    canonical: 'https://freetextconverter.com/unicode-finder'
  }
};

export default function UnicodeFinderPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Script id="schema-unicode-finder" type="application/ld+json" dangerouslySetInnerHTML={{ 
        __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "Unicode Character Finder & Replacer",
          "applicationCategory": "WebApplication",
          "operatingSystem": "Any",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "description": "Free online tool to find and remove hidden Unicode characters, including those generated by AI tools like ChatGPT, zero-width spaces, and other non-printable characters in text.",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "156"
          }
        })
      }} />
      
      <div className="flex-1">
        <div className="container mx-auto">
          {/* Hero Section - Reduced Size */}
          <section className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-indigo-900/30 dark:to-gray-800 rounded-xl shadow-lg mb-8 overflow-hidden">
            <div className="max-w-5xl mx-auto px-6 py-6 text-center relative">
              <h1 className="text-3xl font-bold tracking-tight mb-2 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Unicode Character Finder & Replacer
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-2">
                Find and replace non-printable Unicode characters, zero-width spaces, and other hidden characters in your text.
              </p>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400 max-w-2xl mx-auto">
                Remove invisible Unicode characters generated by AI tools like ChatGPT!
              </p>
            </div>
          </section>

          {/* Main Tool Section */}
          <div className="relative mb-16">
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              {/* Top accent bar */}
              <div className="h-1.5 w-full bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600"></div>

              {/* Content */}
              <div className="p-6">
                <UnicodeFinderClient />
              </div>
            </div>
          </div>

          {/* How to Use Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-16">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">How to Use the Unicode Character Finder</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Paste Your Text</h3>
                    <p className="text-gray-600 dark:text-gray-400">Paste the text you want to analyze into the input field. This can be text from emails, documents, or AI-generated content from tools like ChatGPT.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Find Hidden Characters</h3>
                    <p className="text-gray-600 dark:text-gray-400">Click the &quot;Find Characters&quot; button to detect and highlight all non-printable and special Unicode characters.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Review the Results</h3>
                    <p className="text-gray-600 dark:text-gray-400">Examine the highlighted characters and their Unicode information, including code points and character names.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200">Replace or Remove Characters</h3>
                    <p className="text-gray-600 dark:text-gray-400">Choose to replace specific characters or remove all non-printable characters from your text with a single click.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700 mb-16">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">Frequently Asked Questions</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What are non-printable Unicode characters?</h3>
                <p className="text-gray-600 dark:text-gray-400">Non-printable Unicode characters are special characters that don&apos;t have a visible glyph but can affect text formatting, layout, or behavior. These include zero-width spaces, control characters, and various whitespace characters.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Why do AI tools like ChatGPT leave hidden characters?</h3>
                <p className="text-gray-600 dark:text-gray-400">AI tools like ChatGPT sometimes insert invisible Unicode characters (such as zero-width spaces or special whitespace) to format text or mark content. These characters can cause issues when copying text to other applications or when processing the text further.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Why would I need to find hidden characters?</h3>
                <p className="text-gray-600 dark:text-gray-400">Hidden characters can cause unexpected behavior in text processing, formatting issues in documents, problems with data validation, or even security vulnerabilities. Finding and removing them helps ensure your text behaves as expected.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">What types of characters can this tool detect?</h3>
                <p className="text-gray-600 dark:text-gray-400">This tool can detect a wide range of special characters, including zero-width spaces, non-breaking spaces, control characters, bidirectional text markers, variation selectors, and other invisible or rarely displayed Unicode characters.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Is my data secure when using this tool?</h3>
                <p className="text-gray-600 dark:text-gray-400">Yes, all processing happens in your browser. Your text is never sent to our servers, ensuring complete privacy and security for your sensitive information.</p>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <SupportSection />
        </div>
      </div>
      <Footer />
    </div>
  );
}
