import { NextRequest, NextResponse } from "next/server";
import { processAIRequest } from "@/lib/api";
import { getProvider } from "@/lib/provider-factory";

export async function POST(request: NextRequest) {
  try {
    const { text, level } = await request.json();

    if (!text || !level) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const result = await processAIRequest(async () => {
      const providerName = process.env.HUMANIZER_PROVIDER || "deepseek";
      const modelName = process.env.HUMANIZER_MODEL || "deepseek-chat";
      const apiKey = process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        throw new Error("API key for humanizer is not configured.");
      }
      const provider = getProvider(providerName, apiKey);

      let levelPrompt = "";
      switch (level) {
        case "basic":
          levelPrompt = `Rewrite this text using proper English with correct grammar and spelling. Keep the same length (±15%). Use simple, clear language that is easy to read. Do NOT use slang, informal words, contractions, or casual expressions. Write in standard English.`;
          break;
        case "medium":
          levelPrompt = `Rewrite this text in standard English with proper grammar and spelling. Keep similar length (±15%). Use varied sentence structure and professional language. Make it engaging and readable while maintaining formal quality. Preserve the original meaning.`;
          break;
        case "advanced":
          levelPrompt = `Rewrite this text using sophisticated, professional English with perfect grammar and spelling. Maintain similar length (±15%). Use elegant vocabulary, complex sentence structures, and seamless flow. Create high-quality, polished content that demonstrates excellent writing skills.`;
          break;
      }

      const messages = [
        {
          role: "system",
          content:
            "You are a text humanization assistant. Your task is to rewrite text naturally while strictly maintaining similar length (±15% of input). Provide only the rewritten text without any explanations or comments.",
        },
        {
          role: "user",
          content: `Rewrite the following text while keeping similar length. ${levelPrompt}\n\nText: "${text}"`,
        },
      ];

      const response = await fetch(provider.apiUrl, {
        method: "POST",
        headers: provider.headers,
        body: JSON.stringify(provider.formatBody(modelName, messages)),
      });

      if (!response.ok) {
        throw new Error("Failed to humanize text");
      }

      const data = await response.json();
      return {
        humanizedText: data.choices[0].message.content.trim(),
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Humanize error:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to process request",
      },
      { status: 500 }
    );
  }
}
